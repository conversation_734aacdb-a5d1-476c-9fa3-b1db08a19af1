# IRSWARM_ws - 多光源检测与群体控制系统

## 项目概述

IRSWARM_ws 是一个基于 ROS (Robot Operating System) 的多光源检测与群体控制系统。该项目主要用于实现多个移动机器人的协同控制，通过多相机系统检测光源位置，并基于群体智能算法实现机器人的自主导航和协同行为。

## 系统架构

```
IRSWARM_ws/
├── src/                    # ROS 源代码目录
│   ├── cam/               # 相机检测包
│   │   ├── scripts/       # Python 脚本
│   │   ├── msg/          # 自定义消息类型
│   │   ├── launch/       # 启动文件
│   │   └── package.xml   # 包配置文件
│   ├── mv/               # 移动控制包
│   │   ├── scripts/      # 控制脚本
│   │   ├── msg/         # 消息定义
│   │   └── package.xml  # 包配置文件
│   └── CMakeLists.txt   # 顶层构建文件
├── build/                # 编译输出目录
├── devel/               # 开发环境配置
├── Camera/              # 相机数据存储
├── Data/               # 实验数据存储
└── README.md          # 原始说明文档
```

## 核心功能模块

### 1. 多相机检测系统 (cam 包)

**主要脚本：**
- `cam1.py`, `cam2.py`, `cam3.py`, `cam4.py` - 四个相机节点
- `auto_control.py` - 基于群体智能的自动控制
- `auto_control_sim.py` - 仿真环境控制
- `light_processing.py` - 光源处理算法
- `KF.py` - 卡尔曼滤波器实现

**功能特点：**
- 支持最多4个相机同时工作
- 完整的相机标定系统（内参、外参、畸变校正）
- 实时光源位置检测与跟踪
- 多坐标系转换（像素→相机→机器人→世界坐标系）
- 基于卡尔曼滤波的位置预测
- 群体行为算法实现（分离、对齐、聚集）

### 2. 移动控制系统 (mv 包)

**主要脚本：**
- `auto_control.py` - 自动控制逻辑
- `multi_detect.py` - 多目标检测
- `para_select.py` - 参数选择与调优
- `remake.py` - 系统重构脚本

### 3. 自定义消息类型

**LightInfo.msg：**
```
float32 x          # 光源 X 坐标
float32 y          # 光源 Y 坐标  
float32 distance   # 距离信息
```

**相机消息：**
- `Cam1.msg`, `Cam2.msg`, `Cam3.msg`, `Cam4.msg` - 各相机专用消息

## 群体智能算法

系统实现了经典的 Boids 群体行为算法，包含三个核心规则：

1. **分离 (Separation)** - 避免与邻居过于接近
   - 分离距离：0.8m
   - 权重：2.0（最高优先级）

2. **对齐 (Alignment)** - 与邻居保持相同方向
   - 对齐距离：1.5m  
   - 权重：1.0

3. **聚集 (Cohesion)** - 向邻居群体中心移动
   - 聚集距离：2.0m
   - 权重：0.8

## 硬件要求

### 相机系统
- 支持 MVSDK (Machine Vision SDK)
- 建议使用工业相机
- 需要相机标定参数

### 计算平台
- Ubuntu 18.04/20.04 with ROS Noetic
- NVIDIA Jetson 系列（推荐）
- 支持 CUDA 加速（可选）

### 移动平台
- 支持串口通信的移动机器人
- 需要安装 `ros-noetic-serial` 包

## 安装与部署

### 1. 环境准备
```bash
# 安装 ROS Noetic
wget http://fishros.com/install -O fishros && . fishros

# 安装串口通信包
sudo apt-get install ros-noetic-serial

# 安装手柄控制依赖
sudo apt-get install ros-noetic-joy
pip install paho-mqtt
```

### 2. 相机 SDK 安装
```bash
# 创建 MVSDK 文件夹并解压相机 SDK
mkdir MVSDK
tar -zxvf [相机SDK文件名].tar.gz
```

### 3. 工作空间构建
```bash
cd IRSWARM_ws
catkin_make
source devel/setup.bash
```

### 4. 脚本权限设置
```bash
cd src/cam/scripts
chmod +x *.py
cd ../../mv/scripts
chmod +x *.py
```

### 5. 相机标定配置
```bash
# 检查相机标定参数（在 light_processing.py 中）
# 如需重新标定，可使用以下步骤：

# 1. 准备标定板（棋盘格）
# 2. 采集标定图像
# 3. 使用 OpenCV 标定工具计算内参和畸变系数
# 4. 手眼标定获取外参矩阵
# 5. 更新 light_processing.py 中的标定参数
```

## 使用方法

### 1. 启动系统
```bash
# 启动 ROS 核心
roscore

# 启动完整系统（4个相机 + 控制节点）
roslaunch cam launch.launch
```

### 2. 手柄控制模式
```bash
# 启动手柄节点
rosrun joy joy_node

# 启动手柄控制脚本
rosrun mv stick_control.py
```

### 3. VICON 定位系统集成
```bash
# 启动 VICON 节点（如果有外部定位系统）
roslaunch vrpn_client_ros sample.launch server:=************
```

## 相机标定系统

### 标定参数配置

系统包含完整的多相机标定参数，支持4个相机的内参、外参和畸变校正：

**相机内参矩阵：**
```python
camera_matrix = np.array([[833.33, 0, 640],
                         [0, 833.33, 512],
                         [0, 0, 1]])
```

**畸变系数：**
```python
dist_coeffs = np.array([[-0.097259155290161, 0.099318601051863,
                        -0.012917853790591, 2.737719210785963e-04,
                        -5.326157181954759e-04]])
```

### 多相机外参矩阵

系统为每个小车的4个相机配置了精确的外参矩阵（相机到机器人本体的变换）：

**相机到手部坐标系变换矩阵 (M_cam2hand)：**
- 支持最多8个相机的外参配置
- 每个4x4齐次变换矩阵包含旋转和平移信息
- 用于将相机坐标系下的点转换到机器人坐标系

**相机到小车坐标系变换矩阵 (T_camera_to_car)：**
```python
# 4个相机分别对应不同的安装角度
# 相机1: yaw = 3π/4
# 相机2: yaw = 5π/4
# 相机3: yaw = 7π/4
# 相机4: yaw = 9π/4
```

### 标定功能特性

1. **畸变校正支持**
   - 集成MVSDK的畸变校正功能
   - 支持径向和切向畸变校正
   - 可通过`CameraSetUndistortParams()`设置校正参数

2. **坐标系转换**
   - 像素坐标 → 相机坐标系
   - 相机坐标系 → 机器人本体坐标系
   - 机器人坐标系 → 世界坐标系（VICON）

3. **多相机标定管理**
   - 支持不同小车和相机组合的外参管理
   - 字典结构存储：`{(小车ID, 相机ID): 外参矩阵}`
   - 便于扩展到更多相机配置

### 标定数据存储

- **图像数据：** `Data/` 目录下按时间戳分类存储
- **相机日志：** `Camera/log/` 目录记录相机运行日志
- **标定文件：** 支持`.mat`格式的标定数据导入

## 开发者信息

- **维护者：** chenhaoyu
- **许可证：** TODO
- **ROS 版本：** Noetic
- **Python 版本：** 3.8+

## 注意事项

1. **相机硬件检查**
   - 确保所有相机正确连接并可被系统识别
   - 检查相机固件版本与MVSDK兼容性
   - 验证相机安装位置与外参矩阵一致

2. **标定参数验证**
   - 运行前检查相机内参矩阵是否正确
   - 验证畸变系数的有效性
   - 确认多相机外参矩阵的准确性
   - 定期重新标定以保证精度

3. **坐标系对齐**
   - 确保相机坐标系、机器人坐标系、世界坐标系正确对齐
   - 验证VICON系统与机器人坐标系的变换关系
   - 检查相机安装角度与代码中的角度设置一致

4. **系统调试**
   - 群体控制参数可根据实际需求调整
   - 建议在仿真环境中先测试算法效果
   - 使用标定板验证相机标定精度

## 故障排除

### 相机相关问题
1. **相机无法启动：** 检查 MVSDK 安装和设备权限
2. **图像畸变严重：** 重新标定相机内参和畸变系数
3. **坐标转换错误：** 验证外参矩阵和坐标系定义
4. **多相机同步问题：** 检查相机触发模式和时间同步

### 标定相关问题
5. **标定精度不足：**
   - 增加标定图像数量和角度多样性
   - 使用更高精度的标定板
   - 检查标定板平整度
6. **手眼标定失败：**
   - 确保机器人运动轨迹覆盖工作空间
   - 验证机器人位姿数据的准确性
   - 检查相机与机器人的相对位置固定

### 系统问题
7. **串口通信失败：** 确认串口设备路径和权限
8. **节点通信异常：** 检查 ROS 网络配置和话题名称
9. **性能问题：** 考虑降低相机分辨率或帧率
10. **坐标系不一致：** 统一各模块的坐标系定义

## 扩展功能

- 支持更多相机接入
- 可集成深度学习目标检测
- 支持多种群体行为算法
- 可扩展到3D空间控制
