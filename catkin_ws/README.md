# A-InfraCar 智能车辆跟踪与通信系统

## 项目概述

A-InfraCar 是一个基于 ROS (Robot Operating System) 的智能车辆跟踪与通信系统。该项目主要用于实现车辆的视觉跟踪、信号编解码以及运动控制功能，适用于智能交通、自动驾驶等应用场景。

## 系统架构

```
catkin_ws/
├── build/          # 编译输出目录
├── devel/          # 开发环境配置
└── src/            # 源代码目录
    ├── com/        # 通信模块
    ├── my_package/ # 主要功能包
    └── tracker2/   # 跟踪器模块
```

## 功能模块

### 1. 通信模块 (com)

**主要功能：**
- 信号编解码处理
- 车辆运动控制
- 任务命令管理

**核心文件：**
- `control.py` - 车辆运动控制，支持方向和速度解码
- `decode04.py` - 信号解码器，处理跟踪状态信息
- `encode.py` - 信号编码器
- `command.py` - 命令发布器
- `task_control.py` - 任务控制管理

**消息类型：**
- `ControlCommand.msg` - 控制命令消息（方向+速度）

### 2. 主功能包 (my_package)

**主要功能：**
- 计算机视觉目标检测
- 多目标跟踪
- 图像处理与分析

**核心文件：**
- `tracker.py` / `tracker04.py` - 主跟踪器，集成目标检测和跟踪
- `detect.py` - 图像检测器，基于颜色阈值的目标检测
- `ocsort.py` - OC-SORT 多目标跟踪算法
- `kalmanfilter.py` - 卡尔曼滤波器，用于目标状态预测
- `association.py` - 数据关联算法
- `cam.py` - 相机接口和图像采集

**消息类型：**
- `ImageWithName.msg` - 带名称的图像消息
- `TrackStatus.msg` - 跟踪状态消息

### 3. 跟踪器模块 (tracker2)

**主要功能：**
- 备用跟踪系统
- 相机数据处理
- 目标跟踪算法实现

## 技术特点

### 视觉处理
- **目标检测**：基于颜色阈值的连通区域检测
- **多目标跟踪**：采用 OC-SORT 算法进行实时多目标跟踪
- **状态估计**：使用卡尔曼滤波器进行目标状态预测和平滑
- **数据关联**：智能关联检测结果与跟踪轨迹

### 通信系统
- **信号编解码**：支持自定义信号格式的编解码
- **控制命令**：7位二进制编码（5位方向 + 2位速度）
- **状态同步**：实时跟踪状态信息传输

### 控制系统
- **运动控制**：支持多方向运动控制
- **速度调节**：4级速度控制
- **任务管理**：支持复杂任务序列执行

## 依赖项

### ROS 依赖
- `rospy` - Python ROS 客户端
- `roscpp` - C++ ROS 客户端
- `std_msgs` - 标准消息类型
- `sensor_msgs` - 传感器消息类型
- `geometry_msgs` - 几何消息类型

### Python 依赖
- `opencv-python` - 计算机视觉库
- `numpy` - 数值计算库
- `loguru` - 日志记录
- `cv_bridge` - ROS-OpenCV 桥接

## 快速开始

### 1. 环境准备
```bash
# 确保已安装 ROS
source /opt/ros/noetic/setup.bash

# 进入工作空间
cd /home/<USER>/A-InfraCar/208/catkin_ws

# 编译项目
catkin_make

# 设置环境变量
source devel/setup.bash
```

### 2. 启动系统

**启动通信模块：**
```bash
roslaunch com com.launch
```

**启动跟踪系统：**
```bash
roslaunch my_package trk.launch
```

### 3. 系统监控
```bash
# 查看话题列表
rostopic list

# 监控跟踪状态
rostopic echo /tracker_4

# 监控解码结果
rostopic echo /decoded_4
```

## 系统工作流程

1. **图像采集**：相机节点采集实时图像数据
2. **目标检测**：检测器识别图像中的目标对象
3. **目标跟踪**：跟踪器维护目标轨迹并分配ID
4. **状态编码**：将跟踪状态编码为通信信号
5. **信号传输**：通过ROS话题传输编码信号
6. **信号解码**：解码器解析接收到的信号
7. **控制执行**：根据解码结果执行相应的控制动作

## 配置说明

### 检测参数
- `threshold`: RGB颜色阈值，默认 (40, 40, 40)
- `area_threshold`: 最小检测区域面积，默认 10

### 跟踪参数
- `det_thresh`: 检测置信度阈值
- `max_age`: 轨迹最大存活时间
- `min_hits`: 最小命中次数

### 通信参数
- `base_frequency`: 基础频率，默认 1
- `signal_length`: 信号长度，默认 11
- `repeat_count`: 重复次数，默认 3

## 开发团队

- **维护者**: li
- **邮箱**: <EMAIL>
- **许可证**: TODO

## 注意事项

1. 确保相机设备正确连接
2. 调整检测参数以适应不同光照条件
3. 根据实际需求修改通信协议
4. 定期检查系统日志以监控运行状态

## 扩展功能

- 支持多相机输入
- 深度学习目标检测集成
- 高级路径规划算法
- 分布式多车协同控制
