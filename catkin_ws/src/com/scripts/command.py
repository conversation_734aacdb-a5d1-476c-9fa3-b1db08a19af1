#!/usr/bin/env python3
"""
命令发布器模块 (Command Publisher Module)

功能描述：
- 提供交互式命令行界面用于手动控制
- 接收用户输入的方向和速度参数
- 验证输入参数的有效性
- 发布标准化的控制命令消息
- 支持实时控制和调试测试

使用场景：
- 系统调试和测试
- 手动控制车辆运动
- 验证控制命令的传输和处理
- 开发阶段的功能验证

控制参数：
- 方向：0-31 (32个方向，每个方向对应11.25度)
- 速度：0-3 (4个速度等级，0为停止，3为最高速)

作者：li
日期：2024
"""

import rospy
import sys

# 添加自定义消息包路径
sys.path.append('/home/<USER>/catkin_ws/devel/lib/python3/dist-packages')
from com.msg import ControlCommand

def publish_led_command():
    """
    控制命令发布主函数

    功能：
        1. 初始化ROS节点和发布者
        2. 提供交互式命令行界面
        3. 接收并验证用户输入
        4. 发布控制命令到系统

    交互流程：
        1. 提示用户输入方向值 (0-31)
        2. 提示用户输入速度值 (0-3)
        3. 验证输入参数的有效性
        4. 创建并发布控制命令消息
        5. 循环等待下一次输入

    参数验证：
        - 方向范围：0-31 (对应32个方向)
        - 速度范围：0-3 (对应4个速度等级)
        - 输入类型：必须为整数

    异常处理：
        - 输入格式错误：提示重新输入
        - 参数超出范围：显示错误信息
        - ROS中断：优雅退出程序
    """
    # 初始化ROS节点
    rospy.init_node('com_publisher', anonymous=True)
    rospy.loginfo("控制命令发布器节点已启动")

    # 创建控制命令发布者
    pub = rospy.Publisher('control_command', ControlCommand, queue_size=10)
    rospy.loginfo("控制命令发布者已创建，话题: control_command")

    # 显示使用说明
    print("\n" + "="*60)
    print("           车辆控制命令发布器")
    print("="*60)
    print("方向参数说明：")
    print("  0: 0°   (正东)     8: 90°  (正北)")
    print("  16: 180° (正西)    24: 270° (正南)")
    print("  范围: 0-31 (每个单位对应 11.25°)")
    print("\n速度参数说明：")
    print("  0: 停止    1: 低速    2: 中速    3: 高速")
    print("  范围: 0-3")
    print("\n按 Ctrl+C 退出程序")
    print("="*60 + "\n")

    # 主循环：持续接收用户输入并发布命令
    command_count = 0

    while not rospy.is_shutdown():
        try:
            print(f"\n--- 命令 #{command_count + 1} ---")

            # 获取方向输入
            direction_input = input("请输入方向 (0-31): ").strip()
            if not direction_input:
                print("输入不能为空，请重新输入")
                continue

            direction = int(direction_input)

            # 获取速度输入
            speed_input = input("请输入速度 (0-3): ").strip()
            if not speed_input:
                print("输入不能为空，请重新输入")
                continue

            speed = int(speed_input)

            # 验证方向参数范围
            if not (0 <= direction <= 31):
                raise ValueError(f"方向值 {direction} 超出有效范围 (0-31)")

            # 验证速度参数范围
            if not (0 <= speed <= 3):
                raise ValueError(f"速度值 {speed} 超出有效范围 (0-3)")

            # 创建控制命令消息
            command_msg = ControlCommand()
            command_msg.direction = direction
            command_msg.speed = speed

            # 发布控制命令
            pub.publish(command_msg)

            # 计算实际角度用于显示
            actual_angle = direction * 11.25
            speed_names = ["停止", "低速", "中速", "高速"]

            # 显示发布的命令信息
            rospy.loginfo(f"已发布控制命令 #{command_count + 1}")
            print(f"✓ 命令已发送 - 方向: {direction} ({actual_angle}°), "
                  f"速度: {speed} ({speed_names[speed]})")

            command_count += 1

        except ValueError as e:
            # 处理输入格式错误或参数超出范围
            rospy.logerr(f"输入错误: {e}")
            print(f"❌ 错误: {e}")
            print("请检查输入格式和参数范围，然后重试")

        except KeyboardInterrupt:
            # 处理用户按Ctrl+C的情况
            print("\n\n用户中断，正在退出...")
            rospy.loginfo("用户请求退出控制命令发布器")
            break

        except rospy.ROSInterruptException:
            # 处理ROS系统中断
            rospy.loginfo("ROS系统中断，节点正在关闭")
            print("\nROS系统中断，节点已关闭")
            break

        except Exception as e:
            # 处理其他未预期的异常
            rospy.logerr(f"发生未预期错误: {e}")
            print(f"❌ 系统错误: {e}")
            print("请检查系统状态后重试")

    print(f"\n程序结束，共发送了 {command_count} 个控制命令")
    rospy.loginfo(f"控制命令发布器已退出，共处理 {command_count} 个命令")

if __name__ == '__main__':
    """
    主程序入口

    功能：启动控制命令发布器，处理异常退出
    """
    try:
        # 启动控制命令发布器
        publish_led_command()
    except rospy.ROSInterruptException:
        # 捕获ROS中断异常，正常退出
        pass
    except Exception as e:
        # 捕获其他异常
        rospy.logerr(f"控制命令发布器启动失败: {e}")
        print(f"程序启动失败: {e}")
        pass