#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
车辆运动控制模块 (Vehicle Motion Control Module)

功能描述：
- 接收编码的控制命令并解码为方向和速度信息
- 将解码后的控制信息转换为ROS标准的Twist消息
- 发布速度控制命令到车辆执行器
- 支持8个方向的运动控制和4级速度调节

编码格式：7位二进制
- 前5位：方向编码 (0-31，实际使用0-7表示8个方向)
- 后2位：速度等级 (0-3，表示4个速度档位)

作者：li
日期：2024
"""

import rospy
from geometry_msgs.msg import Twist  # 导入Twist消息类型用于速度控制
from std_msgs.msg import Int32  # 导入Int32消息类型
import math

# 定义一个全局变量来存储速度信息
velocity_data = None

def decode_control_command(control_value):
    """
    解码控制命令函数

    功能：将7位整数控制命令解码为方向和速度信息

    参数：
        control_value (int): 7位整数控制命令 (0-127)

    返回：
        tuple: (direction, speed)
            - direction (int): 方向编码 (0-31，实际使用0-7)
            - speed (int): 速度等级 (0-3)

    编码规则：
        - 前5位表示方向：00000-11111 (0-31)
        - 后2位表示速度：00-11 (0-3)

    示例：
        control_value = 45 (二进制: 0101101)
        -> direction = 11 (二进制: 01011)
        -> speed = 1 (二进制: 01)
    """
    # 将整数转换为7位二进制字符串，确保格式统一
    binary_str = format(control_value, '07b')

    # 提取前5位作为方向信息
    direction_bits = binary_str[:5]
    # 提取后2位作为速度信息
    speed_bits = binary_str[5:]

    # 将二进制字符串转换为整数
    direction = int(direction_bits, 2)  # 方向值：0-31
    speed = int(speed_bits, 2)          # 速度值：0-3

    return direction, speed

def callback(data):
    """
    控制命令回调函数

    功能：处理从ROS话题接收到的控制命令

    参数：
        data (std_msgs.Int32): 包含控制命令的ROS消息

    处理流程：
        1. 解码接收到的控制命令
        2. 根据方向和速度计算线速度和角速度
        3. 发布Twist消息到速度控制话题
    """
    global velocity_data
    rospy.loginfo("接收到速度控制命令 (Received VelocityXY):")

    # 从ROS消息中提取控制命令整数值
    control_value = data.data
    rospy.loginfo(f"原始控制值 (Raw Control Value) = {control_value}")

    # 调用解码函数，将控制命令分解为方向和速度
    direction, speed = decode_control_command(control_value)

    # 输出解码后的方向和速度信息，便于调试
    rospy.loginfo(f"解码结果 - 方向 (Direction): {direction}, 速度等级 (Speed Level): {speed}")

    # 调用发布函数，将解码后的控制信息发送给车辆执行器
    publish_control_command(direction, speed)

def publish_control_command(direction, speed):
    """
    发布车辆控制命令函数

    功能：根据解码后的方向和速度信息生成并发布Twist控制消息

    参数：
        direction (int): 方向编码 (0-31)
        speed (int): 速度等级 (0-3)

    控制逻辑：
        1. 将方向编码转换为角度 (0-360度)
        2. 计算对应的x、y方向速度分量
        3. 生成Twist消息并发布到机器人控制话题

    方向映射：
        - 0: 0度 (正东)
        - 8: 90度 (正北)
        - 16: 180度 (正西)
        - 24: 270度 (正南)
        - 每个单位对应 11.25度 (360/32)

    速度映射：
        - 0: 停止
        - 1: 低速 (0.1倍基础速度)
        - 2: 中速 (0.2倍基础速度)
        - 3: 高速 (0.3倍基础速度)
    """
    # 创建ROS标准的Twist速度控制消息
    control_msg = Twist()

    # 将离散方向编码转换为连续角度值
    # 32个方向对应360度，每个方向间隔11.25度
    angle = direction * (360 / 32)

    # 将角度从度转换为弧度，用于三角函数计算
    angle_rad = math.radians(angle)

    # 根据角度和速度等级计算x、y方向的线速度分量
    # 使用0.1作为基础速度系数，可根据实际需求调整
    base_speed = 0.1
    control_msg.linear.x = speed * math.cos(angle_rad) * base_speed  # x方向速度
    control_msg.linear.y = speed * math.sin(angle_rad) * base_speed  # y方向速度
    control_msg.linear.z = 0.0  # z方向速度（通常为0）

    # 角速度通常设为0，如需转向控制可在此添加
    control_msg.angular.x = 0.0
    control_msg.angular.y = 0.0
    control_msg.angular.z = 0.0

    # 发布控制消息到机器人速度控制话题
    control_publisher.publish(control_msg)

    # 输出详细的控制信息，便于监控和调试
    rospy.loginfo(f"发布控制命令 - 方向: {direction} ({angle}度), 速度等级: {speed}, "
                  f"X速度: {control_msg.linear.x:.3f}, Y速度: {control_msg.linear.y:.3f}")

def velocity_xy_subscriber():
    """
    ROS节点初始化和通信设置函数

    功能：
        1. 初始化ROS节点
        2. 创建控制命令发布者
        3. 创建解码消息订阅者
        4. 启动节点循环

    话题说明：
        - 订阅话题: 'decoded_message' (std_msgs/Int32)
          接收解码后的控制命令
        - 发布话题: '/robot/velcmd' (geometry_msgs/Twist)
          发送速度控制命令给机器人
    """
    global control_publisher  # 声明为全局变量，供回调函数使用

    # 初始化ROS节点，节点名称为'control'
    # anonymous=True 确保节点名称唯一，避免冲突
    rospy.init_node('control', anonymous=True)
    rospy.loginfo("车辆运动控制节点已启动 (Vehicle Motion Control Node Started)")

    # 创建Twist消息发布者，用于发送速度控制命令
    # queue_size=10 设置消息队列大小，防止消息丢失
    control_publisher = rospy.Publisher('/robot/velcmd', Twist, queue_size=10)
    rospy.loginfo("控制命令发布者已创建，话题: /robot/velcmd")

    # 创建Int32消息订阅者，接收解码后的控制命令
    # 当接收到消息时，会自动调用callback函数
    rospy.Subscriber('decoded_message', Int32, callback)
    rospy.loginfo("解码消息订阅者已创建，话题: decoded_message")

    # 保持节点运行，等待消息并处理
    # 直到收到关闭信号（Ctrl+C）才退出
    rospy.loginfo("控制节点运行中，等待控制命令...")
    rospy.spin()

if __name__ == '__main__':
    """
    主程序入口

    功能：启动车辆运动控制节点，处理异常退出
    """
    try:
        # 启动速度控制订阅器
        velocity_xy_subscriber()
    except rospy.ROSInterruptException:
        # 捕获ROS中断异常（如Ctrl+C），优雅退出
        rospy.loginfo("车辆运动控制节点已停止 (Vehicle Motion Control Node Stopped)")
        pass