#!/usr/bin/env python3
"""
信号解码器模块 (Signal Decoder Module)

功能描述：
- 接收来自目标跟踪器的状态信息
- 解码跟踪状态中的二进制信号序列
- 检测特定的信号模式并提取有效数据
- 发布解码后的控制命令到控制系统

解码原理：
- 监听多个跟踪目标的状态变化
- 将状态变化序列作为二进制信号处理
- 使用滑动窗口检测预定义的信号模式
- 提取模式后的数据位并组合成控制命令

信号格式：
- 同步头：[1, 1, 0] 用于信号同步
- 数据位：7位控制命令 (方向5位 + 速度2位)
- 重复传输：提高传输可靠性

作者：li
日期：2024
"""

import rospy
from std_msgs.msg import Int32  # 导入标准消息类型
import threading  # 导入线程模块，用于线程安全
import sys

# 添加自定义消息包路径
sys.path.append('/home/<USER>/catkin_ws/devel/lib/python3/dist-packages')
from my_package.msg import TrackStatus  # 导入自定义消息类型


class DecoderNode:
    """
    信号解码器节点类

    功能：
        1. 订阅跟踪器状态消息
        2. 解码二进制信号序列
        3. 检测信号模式并提取数据
        4. 发布解码后的控制命令
    """

    def __init__(self, base_frequency=1, signal_length=11, repeat_count=3, ID=4):
        """
        初始化解码器节点

        参数：
            base_frequency (int): 基础频率，默认为1
            signal_length (int): 单个信号长度，默认为11位
            repeat_count (int): 重复次数，默认为3次
            ID (int): 跟踪器ID，默认为4
        """
        # 初始化ROS节点
        rospy.init_node('signal_decoder', anonymous=True)
        rospy.loginfo(f"信号解码器节点已启动，跟踪器ID: {ID}")

        # 保存跟踪器ID
        self.tracker_id = ID

        # 创建订阅者，监听指定跟踪器的状态消息
        self.tracker_sub = rospy.Subscriber(f'tracker_{ID}', TrackStatus, self.tracker_callback)
        rospy.loginfo(f"已订阅跟踪器话题: tracker_{ID}")

        # 创建发布者，发布解码后的控制命令
        self.decoded_pub = rospy.Publisher(f'/decoded_{ID}', Int32, queue_size=10)
        rospy.loginfo(f"已创建解码消息发布者: /decoded_{ID}")

        # 信号参数配置
        self.base_frequency = base_frequency    # 基础频率
        self.signal_length = signal_length      # 信号长度
        self.repeat_count = repeat_count        # 重复次数

        # 计算完整数据包长度：(基础频率 × 信号长度 × 重复次数) × 3 + 8
        self.packet = (self.base_frequency * self.signal_length * self.repeat_count) * 3 + 8

        # 定义信号同步模式：[1, 1, 0] 用于识别信号开始
        self.pattern = [1, 1, 0]
        rospy.loginfo(f"信号参数 - 长度: {self.signal_length}, 重复: {self.repeat_count}, "
                      f"数据包长度: {self.packet}, 同步模式: {self.pattern}")

        # 初始化跟踪状态存储
        self.tracking_states = {}  # 存储每个跟踪目标的状态信息
        self.lock = threading.Lock()  # 线程安全锁，防止并发访问冲突

    def tracker_callback(self, data):
        """
        跟踪器状态回调函数

        功能：处理来自跟踪器的状态消息，解析二进制信号

        参数：
            data (TrackStatus): 跟踪状态消息
                - track_id: 跟踪目标ID
                - status: 当前状态值 (0或1，作为二进制位)

        处理流程：
            1. 提取跟踪ID和状态位
            2. 将状态位添加到对应目标的缓冲区
            3. 检测同步模式 [1,1,0]
            4. 收集完整的数据包
            5. 启动解码线程处理数据
        """
        try:
            # 从消息中提取跟踪ID和状态位
            tracking_id = data.track_id
            received_bit = data.status

            # 记录接收到的原始数据，便于调试
            rospy.logdebug(f"接收到信号位 - ID: {tracking_id}, 位值: {received_bit}")

            # 将状态值转换为二进制位 (0 或 1)
            bit = int(received_bit)

            # 使用线程锁确保多线程安全访问共享数据
            with self.lock:
                # 为新的跟踪目标初始化状态
                if tracking_id not in self.tracking_states:
                    self.tracking_states[tracking_id] = {
                        'buffer': [],           # 二进制数据缓冲区
                        'is_collecting': False, # 是否正在收集数据包标志
                        'last_update': rospy.Time.now()  # 最后更新时间
                    }
                    rospy.loginfo(f"为跟踪目标 {tracking_id} 初始化状态缓冲区")

                # 获取当前跟踪目标的状态
                state = self.tracking_states[tracking_id]
                state['last_update'] = rospy.Time.now()

                # 将新接收的二进制位添加到缓冲区
                state['buffer'].append(bit)

                # 限制缓冲区大小，防止内存溢出
                max_buffer_size = self.packet + len(self.pattern) + 10
                if len(state['buffer']) > max_buffer_size:
                    state['buffer'] = state['buffer'][-max_buffer_size:]

                # 检测同步模式：当缓冲区足够长且未在收集数据时
                if len(state['buffer']) >= len(self.pattern) and not state['is_collecting']:
                    # 使用滑动窗口检查最新的几位是否匹配同步模式
                    window = state['buffer'][-len(self.pattern):]

                    if window == self.pattern:
                        # 检测到同步模式 [1,1,0]，开始收集数据包
                        state['is_collecting'] = True

                        # 清空缓冲区，准备收集新的数据包
                        # 保留模式的最后一位作为数据包的第一位
                        last_bit = state['buffer'][-1]
                        state['buffer'] = [last_bit]

                        rospy.loginfo(f"跟踪目标 {tracking_id} - 检测到同步模式 {self.pattern}，开始收集数据包")

                # 如果正在收集数据包
                if state['is_collecting']:
                    # 检查是否收集到完整的数据包
                    if len(state['buffer']) >= self.packet:
                        # 提取完整的信号数据包
                        signal = state['buffer'][:self.packet]
                        rospy.loginfo(f"跟踪目标 {tracking_id} - 收集到完整数据包，长度: {len(signal)}")
                        rospy.logdebug(f"数据包内容: {signal}")

                        # 重置收集状态，准备下一次收集
                        state['is_collecting'] = False
                        state['buffer'] = []

                        # 在新线程中进行解码处理，避免阻塞主线程
                        decode_thread = threading.Thread(
                            target=self.decode_and_publish,
                            args=(tracking_id, signal),
                            daemon=True,  # 守护线程，主程序退出时自动结束
                            name=f"decoder_thread_{tracking_id}"
                        )
                        decode_thread.start()
                        rospy.logdebug(f"启动解码线程处理跟踪目标 {tracking_id} 的数据包")

        except Exception as e:
            # 捕获并记录处理过程中的异常
            rospy.logerr(f"处理跟踪消息时发生错误: {e}")
            rospy.logerr(f"错误详情 - 跟踪ID: {tracking_id if 'tracking_id' in locals() else 'Unknown'}")

    def decode_and_publish(self, tracking_id, signal):
        """
        信号解码和发布函数

        功能：
            1. 对接收到的信号进行解码处理
            2. 使用投票机制确保解码结果的可靠性
            3. 发布最终的控制命令

        参数：
            tracking_id (int): 跟踪目标ID
            signal (list): 原始二进制信号序列

        解码流程：
            1. 调用信号解码函数处理原始信号
            2. 检查解码结果的数量和一致性
            3. 使用多数投票机制选择最可靠的结果
            4. 发布最终的控制命令到ROS话题

        容错机制：
            - 要求至少3个解码结果
            - 如果3个结果完全不同，则认为解码失败
            - 使用投票机制选择出现频率最高的结果
        """
        try:
            # 调用信号解码函数，获取解码结果列表
            decoded_messages = self.decode_signal(signal)

            if decoded_messages:
                # 检查解码结果数量是否符合预期（应该有3个重复传输的结果）
                if len(decoded_messages) == 3:
                    # 检查三个解码结果的一致性
                    unique_values = set(decoded_messages)

                    if len(unique_values) == 3:
                        # 如果三个值完全不同，说明解码失败
                        rospy.logwarn(f"跟踪目标 {tracking_id} - 解码失败：三个结果完全不同 {decoded_messages}")
                    else:
                        # 使用投票机制选择最可靠的结果
                        from collections import Counter
                        counter = Counter(decoded_messages)
                        most_common_value, frequency = counter.most_common(1)[0]

                        # 发布解码后的控制命令
                        with self.lock:
                            msg = Int32()
                            msg.data = most_common_value
                            self.decoded_pub.publish(msg)

                            rospy.loginfo(f"跟踪目标 {tracking_id} - 成功发布解码消息: {most_common_value} "
                                        f"(投票结果: {frequency}/{len(decoded_messages)})")
                            rospy.logdebug(f"所有解码结果: {decoded_messages}")
                else:
                    # 解码结果数量不符合预期
                    rospy.logwarn(f"跟踪目标 {tracking_id} - 解码结果数量异常: 期望3个，实际{len(decoded_messages)}个")
                    rospy.logdebug(f"异常解码结果: {decoded_messages}")
            else:
                # 没有有效的解码结果
                rospy.logwarn(f"跟踪目标 {tracking_id} - 未获得有效的解码结果")

        except Exception as e:
            rospy.logerr(f"跟踪目标 {tracking_id} - 解码和发布过程中发生错误: {e}")

    def preprocess_signal(self, signal):
        """
        信号预处理函数

        功能：对原始信号进行预处理，消除噪声和采样不均匀的影响

        参数：
            signal (list): 原始二进制信号序列

        返回：
            list: 预处理后的理想化信号序列

        处理流程：
            1. 初始聚类：将连续相同的值聚合成簇
            2. 特殊处理：对第一个簇进行特殊拆分（处理起始信号）
            3. 过采样处理：处理采样不均匀的问题
            4. 生成理想化序列：提取有效的信号位

        算法说明：
            - 聚类可以消除短暂的噪声干扰
            - 特殊拆分处理信号起始部分的特殊情况
            - 过采样处理解决不同采样率的问题
        """
        try:
            # 第一步：对原始信号进行初始聚类
            # 将连续相同的值合并为 (值, 计数) 的形式
            clusters = self.cluster_samples(signal)
            rospy.logdebug(f"初始聚类结果: {clusters}")

            # 第二步：对第一个簇进行特殊处理
            # 处理信号起始部分可能存在的特殊情况
            clusters = self.split_first_cluster(clusters)
            rospy.logdebug(f"第一簇拆分后: {clusters}")

            # 第三步：处理过采样问题
            # 将大的簇拆分成更小的单元，解决采样不均匀问题
            processed_clusters = self.split_large_clusters(clusters)
            rospy.logdebug(f"过采样处理后: {processed_clusters}")

            # 第四步：生成理想化的信号序列
            # 只保留计数大于1的簇，过滤掉可能的噪声
            ideal_samples = [val for val, cnt in processed_clusters if cnt > 1]

            rospy.logdebug(f"预处理完成，原始长度: {len(signal)}, 处理后长度: {len(ideal_samples)}")
            return ideal_samples

        except Exception as e:
            rospy.logerr(f"信号预处理过程中发生错误: {e}")
            return signal  # 返回原始信号作为备选

    def split_first_cluster(self, clusters):
        """
        第一簇特殊拆分函数

        功能：对第一个聚类簇进行特殊处理，解决信号起始部分的特殊情况

        参数：
            clusters (list): 聚类结果列表，格式为 [(值, 计数), ...]

        返回：
            list: 处理后的聚类结果

        处理逻辑：
            - 如果第一个簇的样本数 > 4，则拆分为两部分
            - 前3个样本作为一个独立的簇
            - 剩余样本作为另一个簇
            - 这样可以更好地处理信号起始部分的同步模式
        """
        # 安全性检查：确保clusters不为空且格式正确
        if not clusters or len(clusters[0]) != 2:
            rospy.logwarn("聚类结果格式异常，跳过第一簇拆分")
            return clusters

        first_val, first_count = clusters[0]

        # 如果第一个簇的计数大于4，进行拆分
        if first_count > 4:
            rospy.logdebug(f"拆分第一簇: 值={first_val}, 原计数={first_count}")
            return [
                (first_val, 3),                    # 前3个样本
                (first_val, first_count - 5),      # 剩余样本（减去前3个和2个缓冲）
                *clusters[1:]                       # 保留其他所有簇
            ]

        # 如果不需要拆分，返回原始聚类结果
        return clusters

    def clusters_to_signal(self, clusters):
        """
        聚类结果转信号函数

        功能：将聚类结果转换回完整的信号序列

        参数：
            clusters (list): 聚类结果，格式为 [(值, 计数), ...]

        返回：
            list: 重构的信号序列

        用途：
            - 用于调试和验证聚类结果
            - 将压缩的聚类表示还原为原始信号格式
        """
        signal = []
        for value, count in clusters:
            # 将每个簇按照其计数展开为重复的值
            signal.extend([value] * count)
        return signal

    # 修改后的解码方法
    def decode_signal(self, signal):
        """解码信号（添加预处理）"""
        # 新增预处理步骤
        processed_signal = self.preprocess_signal(signal)
        rospy.loginfo(f"Processed signal: {processed_signal}")
        
        decoded_messages = []
        start_index = 0
        end_index = 9
        
        while start_index + self.signal_length <= len(processed_signal):
            message_packet = processed_signal[start_index:start_index + self.signal_length]
            end, flag = self.decode_message_packet(message_packet, end_index)
            if flag:
                decoded_messages.append(flag)
                start_index += end + 2
                end_index = end
            elif end:
                start_index += end + 2
                end_index = end
            else:
                start_index += 1
                end_index = start_index + 9
        return decoded_messages if decoded_messages else None

    # 原有聚类和拆分方法保持不变
    def cluster_samples(self, signal):
        """信号采样聚类（合并连续相同值）"""
        if not signal:
            return []
        clusters = []
        current_val = signal[0]
        count = 1
        for s in signal[1:]:
            if s == current_val:
                count += 1
            else:
                clusters.append((current_val, count))
                current_val = s
                count = 1
        clusters.append((current_val, count))
        return clusters

    def split_large_clusters(self, clusters, oversample_factor=3, tolerance=1.5):
        """处理过采样的大样本簇"""
        new_clusters = []
        for value, count in clusters:
            if count >= oversample_factor * tolerance:
                num_samples = max(1, round(count / oversample_factor))
                for _ in range(num_samples):
                    new_clusters.append((value, oversample_factor))
            else:
                new_clusters.append((value, count))
        return new_clusters


    def decode_message_packet(self, message_packet,end_index):
        """
        解码消息包。
        """
        try:
            # 1. 查找结束位
            end_index, flag2 = self.find_end_index_in_packet(message_packet,end_index,end_index+3)
            rospy.loginfo(f"End index: {end_index}, Flag2: {flag2}")

            # 2. 进行奇校验
            is_valid = self.parity_check(message_packet)
            rospy.loginfo(f"Parity check result: {is_valid}")

            if is_valid:
                # 3. 提取有效消息
                valid_message = message_packet[1:-3]  # 提取有效消息（去掉起始位和校验位）
                rospy.loginfo(f"Valid message: {valid_message}")

                # 4. 解码消息
                decoded_value = self.decode_binary_message(valid_message)
                return end_index, decoded_value
            elif flag2 == 0:
                rospy.logwarn("Parity check failed. Discarding message.")
                return end_index, None
            else:
                rospy.logwarn("End pattern not found. Discarding message.")
                return None, None
        except Exception as e:
            rospy.logerr(f"Error decoding message packet: {e}")
            return None

    def find_end_index_in_packet(self, message_packet, search_window_start=9, search_window_end=12, pattern=[1, 1, 0]):
        """
        在消息包中查找结束位。如果没有找到结束位，返回默认结束位。
        """
        flag2 = 1
        search_start = search_window_start
        search_end = min(search_window_end, len(message_packet))  # 限制最大搜索范围
        end_index = search_window_start   # 默认结束位为 Pattern indices + 8（相对于消息包）

        # 在消息包范围内查找结束位
        for i in range(search_start, search_end - 2):
            if message_packet[i:i + 3] == pattern:
                end_index = i + 2  # 结束位设置为 110 的结束位置
                flag2 = 0
                break

        return end_index, flag2

    def parity_check(self, message):
        """
        在起始位与结束位之间进行奇校验。
        """
        # 起始位后第7位为数据位
        data_start = 1
        # 校验位为结束位前一位
        parity_index = 8
        
        # 提取数据位
        data_bits = message[data_start:parity_index]
        if len(data_bits) == 0:  # 确保数据位存在
            raise ValueError("数据位长度为 0，无法进行校验。")
        
        # 计算数据位的奇校验结果
        parity_bit = message[parity_index]
        parity_calculated = 1 if data_bits.count(1) % 2 == 0 else 0  # 奇校验：求和后取模 2
        
        # 校验是否通过
        return parity_calculated == parity_bit

    def decode_binary_message(self, binary_message):
        """
        将二进制消息解码为整数值。
        """
        try:
            binary_str = ''.join(map(str, binary_message))  # 将二进制列表转换为字符串
            decoded_value = int(binary_str, 2)  # 将二进制字符串转换为整数
            rospy.loginfo(f"Pdecoded_value={decoded_value}")

            return decoded_value
        except Exception as e:
            rospy.logerr(f"Error decoding binary message: {e}")
            return None


if __name__ == '__main__':
    try:
        decoder_node = DecoderNode()
        rospy.spin()
    except rospy.ROSInterruptException:
        pass