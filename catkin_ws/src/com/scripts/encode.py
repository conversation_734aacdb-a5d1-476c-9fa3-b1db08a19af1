#!/usr/bin/env python3
"""
信号编码器模块 (Signal Encoder Module)

功能描述：
- 接收控制命令（方向和速度）
- 将控制命令编码为二进制信号序列
- 使用差分曼彻斯特编码提高传输可靠性
- 通过LED或其他输出设备发送编码信号
- 支持重复传输和错误校验

编码格式：
- 起始位：0 (信号开始标识)
- 数据位：7位 (5位方向 + 2位速度)
- 校验位：1位 (奇偶校验)
- 停止位：11 (信号结束标识)

传输协议：
- 使用差分曼彻斯特编码
- 三次重复传输提高可靠性
- 传输间隔使用默认值分隔

作者：li
日期：2024
"""

import rospy
from std_msgs.msg import Int8
import sys

# 添加自定义消息包路径
sys.path.append('/home/<USER>/catkin_ws/devel/lib/python3/dist-packages')
from com.msg import ControlCommand  # 导入自定义控制命令消息


def d_m_encode(data):
    """
    差分曼彻斯特编码函数

    功能：将二进制数据转换为差分曼彻斯特编码序列

    参数：
        data (str): 二进制数据字符串，如 "0110101"

    返回：
        list: 编码后的电平序列，包含0和1

    编码规则：
        - 每个数据位开始时都有一个跳变（时钟同步）
        - 数据位'1'：在位中间有额外跳变
        - 数据位'0'：在位中间无额外跳变
        - 这种编码方式具有自同步特性，抗干扰能力强

    示例：
        输入: "101"
        输出: [0, 1, 0, 0, 1, 1, 0] (起始电平 + 编码序列)
    """
    encoded = []
    previous_level = 0  # 初始电平设为0
    encoded.append(previous_level)  # 添加起始电平

    for bit in data:
        # 每个位开始时都有跳变（提供时钟同步）
        previous_level = 1 - previous_level  # 电平翻转
        encoded.append(previous_level)

        # 根据数据位决定是否在中间跳变
        if bit == '1':
            # 数据位为'1'时，在位中间产生额外跳变
            previous_level = 1 - previous_level
        # 数据位为'0'时，位中间不跳变，保持当前电平

        encoded.append(previous_level)

    return encoded

def encode_message(direction, speed):
    """
    控制消息编码函数

    功能：将方向和速度控制参数编码为完整的二进制消息

    参数：
        direction (int): 方向编码 (0-31)，表示32个可能的方向
        speed (int): 速度等级 (0-3)，表示4个速度档位

    返回：
        list: 差分曼彻斯特编码后的信号序列

    消息格式：
        [起始位][方向5位][速度2位][校验位][停止位]
        总长度：1 + 5 + 2 + 1 + 2 = 11位

    校验机制：
        使用奇偶校验确保数据传输的正确性
        奇校验：数据位中'1'的个数为奇数时校验位为'0'，否则为'1'

    异常处理：
        - 方向值超出范围 (0-31) 时抛出异常
        - 速度值超出范围 (0-3) 时抛出异常
    """
    # 参数有效性检查
    if not (0 <= direction <= 31):
        raise ValueError(f"方向参数无效: {direction}，必须在0到31之间")
    if not (0 <= speed <= 3):
        raise ValueError(f"速度参数无效: {speed}，必须在0到3之间")

    # 【编码步骤1】将方向编码为5位二进制
    # 示例：direction = 12 → "01100" (5位)
    direction_bits = f"{direction:05b}"

    # 【编码步骤2】将速度编码为2位二进制
    # 示例：speed = 2 → "10" (2位)
    speed_bits = f"{speed:02b}"

    # 【编码步骤3】组合数据位（总共7位）
    # 示例：data_bits = "01100" + "10" = "0110010" (7位)
    data_bits = direction_bits + speed_bits

    # 【编码步骤4】计算奇偶校验位
    # 统计数据位中'1'的个数，实现奇校验
    # 示例：data_bits = "0110010" 中有3个'1'，奇数个，校验位=0
    ones_count = data_bits.count('1')
    parity_bit = '1' if ones_count % 2 == 0 else '0'

    # 【编码步骤5】构建完整的消息帧
    # 示例：complete_message = "0" + "0110010" + "0" + "11" = "00110010011" (11位)
    start_bit = '0'      # 起始位，标识消息开始
    stop_bits = '11'     # 停止位，标识消息结束
    complete_message = start_bit + data_bits + parity_bit + stop_bits

    # 记录编码信息，便于调试
    rospy.loginfo(f"编码消息 - 方向: {direction}({direction_bits}), "
                  f"速度: {speed}({speed_bits}), 校验位: {parity_bit}")
    rospy.loginfo(f"完整二进制消息: {complete_message}")

    # 应用差分曼彻斯特编码
    dm_encoded = d_m_encode(complete_message)
    rospy.logdebug(f"差分曼彻斯特编码结果: {dm_encoded}")

    return dm_encoded

def control_command_callback(data):
    """
    控制命令回调函数

    功能：处理接收到的控制命令并进行编码传输

    参数：
        data (ControlCommand): 控制命令消息
            - direction: 方向值 (0-31)
            - speed: 速度值 (0-3)

    传输协议：
        1. 三次重复传输提高可靠性
        2. 每次传输之间插入分隔信号
        3. 使用固定频率确保时序准确

    传输序列：
        [编码消息] -> [分隔符] -> [编码消息] -> [分隔符] -> [编码消息]
    """
    try:
        # 从控制命令消息中提取方向和速度参数
        direction = data.direction
        speed = data.speed

        rospy.loginfo(f"接收到控制命令 - 方向: {direction}, 速度: {speed}")

        # 对控制参数进行编码
        encoded_message = encode_message(direction, speed)
        rospy.loginfo(f"编码完成，信号长度: {len(encoded_message)}")

        # 设置消息接收标志，暂停默认信号发送
        global received_message
        received_message = True

        # 执行三次重复传输协议
        for transmission_round in range(3):
            rospy.loginfo(f"开始第 {transmission_round + 1} 次传输")

            # 发送编码后的完整消息
            for bit_index, bit in enumerate(encoded_message):
                led_cmd = Int8()
                led_cmd.data = int(bit)
                pub.publish(led_cmd)
                rospy.logdebug(f"发送位 {bit_index + 1}/{len(encoded_message)}: {bit}")
                rate.sleep()  # 按照设定频率发送

            # 在传输之间发送分隔信号（除了最后一次传输）
            if transmission_round < 2:
                rospy.logdebug("发送传输间隔分隔符")
                for separator_bit in range(4):
                    led_cmd = Int8()
                    led_cmd.data = 1  # 使用高电平作为分隔符
                    pub.publish(led_cmd)
                    rospy.logdebug(f"发送分隔符位 {separator_bit + 1}/4: 1")
                    rate.sleep()

        rospy.loginfo("控制命令传输完成")

        # 重置消息接收标志，恢复默认信号发送
        received_message = False

    except Exception as e:
        rospy.logerr(f"处理控制命令时发生错误: {e}")
        received_message = False


def publish_led_command():
    """
    LED命令发布主函数

    功能：
        1. 初始化ROS节点和通信接口
        2. 设置信号发布频率
        3. 订阅控制命令话题
        4. 在空闲时发送默认信号

    通信接口：
        - 发布话题: 'robot/ledcmd' (std_msgs/Int8)
          用于发送LED控制信号
        - 订阅话题: 'control_command' (com/ControlCommand)
          接收控制命令

    工作模式：
        - 正常模式：发送默认信号 (高电平)
        - 传输模式：发送编码的控制命令
    """
    # 初始化ROS节点
    rospy.init_node('led_publisher', anonymous=True)
    rospy.loginfo("LED信号编码器节点已启动")

    # 声明全局变量
    global pub, rate, received_message

    # 创建LED命令发布者
    pub = rospy.Publisher('robot/ledcmd', Int8, queue_size=10)
    rospy.loginfo("LED命令发布者已创建，话题: robot/ledcmd")

    # 设置信号发布频率
    # 10 Hz 提供足够的时间分辨率，同时不会过度占用带宽
    rate = rospy.Rate(10)
    rospy.loginfo("信号发布频率设置为 10 Hz")

    # 订阅控制命令话题
    rospy.Subscriber('control_command', ControlCommand, control_command_callback)
    rospy.loginfo("已订阅控制命令话题: control_command")

    # 初始化消息接收状态标志
    received_message = False

    # 主循环：在没有控制命令时发送默认信号
    rospy.loginfo("进入主循环，等待控制命令...")

    while not rospy.is_shutdown():
        # 当没有正在处理控制命令时，发送默认的高电平信号
        if not received_message:
            led_cmd = Int8()
            led_cmd.data = 1  # 默认高电平
            pub.publish(led_cmd)
            rospy.logdebug("发送默认信号: 1")

        # 按照设定频率休眠
        rate.sleep()


if __name__ == '__main__':
    """
    主程序入口

    功能：启动LED信号编码器节点，处理异常退出
    """
    try:
        # 启动LED命令发布器
        publish_led_command()
    except rospy.ROSInterruptException:
        # 捕获ROS中断异常（如Ctrl+C），优雅退出
        rospy.loginfo("LED信号编码器节点已停止")
        pass
    except Exception as e:
        # 捕获其他异常
        rospy.logerr(f"LED信号编码器节点发生未预期错误: {e}")
        pass