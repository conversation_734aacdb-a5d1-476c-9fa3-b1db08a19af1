#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
任务控制管理模块 (Task Control Management Module)

功能描述：
- 统一管理多摄像头的任务控制逻辑
- 解码任务命令并执行相应的运动控制
- 支持搜索、返回、退出三种基本任务模式
- 根据摄像头位置和任务类型计算运动方向
- 提供实时的速度控制和状态管理

任务模式：
- search (搜索模式): 值=81, 远离检测到目标的方向
- return (返回模式): 值=45, 朝向检测到目标的方向
- exit (退出模式): 值=91, 停止所有运动

摄像头配置：
- 4个摄像头分别位于车辆的四个方向
- 每个摄像头有固定的朝向角度
- 根据触发的摄像头确定运动方向

作者：li
日期：2024
"""

import rospy, math, numpy as np
from std_msgs.msg     import Int32
from geometry_msgs.msg import Twist

# ———————— 系统参数配置区 ————————

# 摄像头朝向角度配置 (弧度制)
# 定义4个摄像头的安装朝向，用于计算运动方向
camera_yaws = np.array([
    -3*math.pi/4,   # cam0: 西北方向 (-135°)
     -math.pi/4,    # cam1: 东北方向 (-45°)
     math.pi/4,     # cam2: 东南方向 (45°)
     3*math.pi/4    # cam3: 西南方向 (135°)
])

# 基础线速度设置 (m/s)
V0 = 0.3   # 标准移动速度，可根据实际需求调整

# ———————— 全局状态变量 ————————

current_command = None   # 当前任务命令: 'search' / 'return' / 'exit' / None
cmd_pub = None          # Twist消息发布器，用于发送速度控制命令

# ———————— 辅助函数定义 ————————

def normalize_angle(angle):
    """
    角度归一化函数

    功能：将任意角度值归一化到 [-π, π] 范围内

    参数：
        angle (float): 输入角度值 (弧度)

    返回：
        float: 归一化后的角度值 (弧度)

    用途：
        - 确保角度计算的一致性
        - 避免角度值超出标准范围
        - 简化角度比较和计算
    """
    return (angle + math.pi) % (2*math.pi) - math.pi

def decode_task_command(command_value):
    """
    任务命令解码函数

    功能：将数值型命令码转换为可读的任务字符串

    参数：
        command_value (int): 数值型任务命令码

    返回：
        str or None: 对应的任务命令字符串，无效命令返回None

    命令码映射：
        81 → 'search' (搜索模式)
        45 → 'return' (返回模式)
        91 → 'exit'   (退出模式)

    设计说明：
        - 使用特定数值避免与其他控制命令冲突
        - 便于在通信协议中传输和识别
        - 支持后续扩展更多任务类型
    """
    command_map = {
        81: 'search',  # 搜索任务：远离目标方向移动
        45: 'return',  # 返回任务：朝向目标方向移动
        91: 'exit'     # 退出任务：停止所有运动
    }

    return command_map.get(command_value, None)

# ———————— 核心回调函数 ————————

def unified_cb(msg, camera_source):
    """
    统一消息回调处理函数

    功能：处理来自不同摄像头的解码消息，执行相应的任务控制

    参数：
        msg (Int32): 解码后的消息，包含任务命令或触发信号
        camera_source (int): 摄像头编号 (1-4)

    处理逻辑：
        1. 解码任务命令 (search/return/exit)
        2. 根据触发的摄像头确定运动方向
        3. 根据任务类型调整运动策略
        4. 计算并发布速度控制命令

    任务策略：
        - search: 远离检测目标的方向移动
        - return: 朝向检测目标的方向移动
        - exit: 立即停止所有运动
    """
    # 将摄像头编号转换为数组索引 (1-4 → 0-3)
    camera_index = camera_source - 1
    global current_command

    try:
        # 解码接收到的任务命令
        decoded_command = decode_task_command(msg.data)

        if decoded_command:
            # 更新当前任务状态
            current_command = decoded_command
            rospy.loginfo(f"[任务命令] 接收到新任务: {current_command} (来源: 摄像头{camera_source})")

            # 处理退出命令：立即停止运动
            if current_command == 'exit':
                stop_twist = Twist()  # 创建零速度命令
                cmd_pub.publish(stop_twist)
                rospy.loginfo("[任务控制] 执行退出命令，车辆已停止")
                return

        # 检查是否有有效的触发信号和任务命令
        if not msg.data or current_command not in ('search', 'return'):
            # 无有效信号或任务，发送停止命令
            stop_twist = Twist()
            cmd_pub.publish(stop_twist)
            rospy.logdebug(f"[摄像头{camera_source}] 无有效任务或信号，车辆停止")
            return

        # 获取触发摄像头的朝向角度
        camera_yaw = camera_yaws[camera_index]

        # 根据任务类型调整运动方向
        if current_command == 'search':
            # 搜索模式：远离目标方向 (反向180度)
            movement_yaw = camera_yaw + math.pi
            rospy.logdebug(f"[搜索模式] 摄像头{camera_source} 检测到目标，远离方向移动")
        else:  # current_command == 'return'
            # 返回模式：朝向目标方向
            movement_yaw = camera_yaw
            rospy.logdebug(f"[返回模式] 摄像头{camera_source} 检测到目标，朝向方向移动")

        # 角度归一化处理
        movement_yaw = normalize_angle(movement_yaw)

        # 将运动方向分解为x、y方向的速度分量
        velocity_x = V0 * math.cos(movement_yaw)
        velocity_y = V0 * math.sin(movement_yaw)

        # 创建并发布Twist速度控制消息
        twist_command = Twist()
        twist_command.linear.x = velocity_x
        twist_command.linear.y = velocity_y
        twist_command.linear.z = 0.0

        # 不使用角速度控制，保持车辆朝向稳定
        twist_command.angular.x = 0.0
        twist_command.angular.y = 0.0
        twist_command.angular.z = 0.0

        # 发布速度控制命令
        cmd_pub.publish(twist_command)

        # 记录详细的控制信息
        angle_degrees = math.degrees(movement_yaw)
        rospy.loginfo(f"[摄像头{camera_source}] {current_command}模式 → "
                     f"方向: {angle_degrees:.1f}°, 速度: vx={velocity_x:.2f}, vy={velocity_y:.2f}")

    except Exception as e:
        # 异常处理：发生错误时停止车辆
        rospy.logerr(f"[任务控制] 处理摄像头{camera_source}消息时发生错误: {e}")
        stop_twist = Twist()
        cmd_pub.publish(stop_twist)

# ———————— 主程序入口 ————————

def main():
    """
    任务控制系统主函数

    功能：
        1. 初始化ROS节点和通信接口
        2. 创建速度控制发布者
        3. 订阅多个摄像头的解码消息
        4. 启动消息循环处理

    系统架构：
        - 监听4个摄像头的解码消息 (/decoded_1 到 /decoded_4)
        - 统一处理所有摄像头的触发信号
        - 发布速度控制命令到 /robot/velcmd

    容错设计：
        - 支持摄像头动态上下线
        - 异常情况下自动停止车辆
        - 提供详细的日志记录
    """
    global cmd_pub

    # 初始化ROS节点
    rospy.init_node('task_cam_unified_controller', anonymous=True)
    rospy.loginfo("任务控制管理节点已启动")

    # 创建速度控制命令发布者
    cmd_pub = rospy.Publisher('/robot/velcmd', Twist, queue_size=1)
    rospy.loginfo("速度控制发布者已创建，话题: /robot/velcmd")

    # 订阅所有摄像头的解码消息
    subscribers = []
    for camera_id in range(1, 5):
        topic_name = f'/decoded_{camera_id}'
        subscriber = rospy.Subscriber(
            topic_name,
            Int32,
            unified_cb,
            callback_args=camera_id
        )
        subscribers.append(subscriber)
        rospy.loginfo(f"已订阅摄像头{camera_id}解码消息: {topic_name}")

    # 显示系统配置信息
    rospy.loginfo("="*50)
    rospy.loginfo("任务控制系统配置:")
    rospy.loginfo(f"  基础速度: {V0} m/s")
    rospy.loginfo(f"  摄像头数量: {len(camera_yaws)}")
    for i, yaw in enumerate(camera_yaws):
        angle_deg = math.degrees(yaw)
        rospy.loginfo(f"  摄像头{i+1}: {angle_deg:.1f}°")
    rospy.loginfo("  任务命令: search(81), return(45), exit(91)")
    rospy.loginfo("="*50)

    # 启动ROS消息循环
    rospy.loginfo("任务控制系统运行中，等待摄像头信号...")
    rospy.spin()

if __name__ == '__main__':
    """
    程序主入口

    功能：启动任务控制管理系统，处理异常退出
    """
    try:
        # 启动主程序
        main()
    except rospy.ROSInterruptException:
        # 正常的ROS中断退出
        rospy.loginfo("任务控制管理节点已停止")
        pass
    except Exception as e:
        # 处理其他异常
        rospy.logerr(f"任务控制管理节点发生错误: {e}")
        pass
