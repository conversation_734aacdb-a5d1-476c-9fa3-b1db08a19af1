#!/usr/bin/env python3
"""
编码示例验证脚本
验证 direction=12, speed=2 的编码过程
"""

def test_encoding_example():
    # 输入参数
    direction = 12  # 对应 135° 方向
    speed = 2       # 中速
    
    print("=== 控制命令编码示例验证 ===")
    print(f"输入: direction={direction}, speed={speed}")
    print()
    
    # 步骤1：方向二进制编码
    direction_bits = f"{direction:05b}"
    print(f"步骤1 - 方向二进制: {direction} → {direction_bits} (5位)")
    
    # 步骤2：速度二进制编码
    speed_bits = f"{speed:02b}"
    print(f"步骤2 - 速度二进制: {speed} → {speed_bits} (2位)")
    
    # 步骤3：数据位组合
    data_bits = direction_bits + speed_bits
    print(f"步骤3 - 数据位组合: {direction_bits} + {speed_bits} = {data_bits} (7位)")
    
    # 步骤4：计算校验位
    ones_count = data_bits.count('1')
    parity_bit = '1' if ones_count % 2 == 0 else '0'
    print(f"步骤4 - 校验位计算: 数据位中有{ones_count}个'1'，{'偶数' if ones_count % 2 == 0 else '奇数'}个，校验位={parity_bit}")
    
    # 步骤5：完整消息帧
    start_bit = '0'
    stop_bits = '11'
    complete_message = start_bit + data_bits + parity_bit + stop_bits
    print(f"步骤5 - 完整消息帧: {start_bit} + {data_bits} + {parity_bit} + {stop_bits} = {complete_message} (11位)")
    
    print()
    print("=== 消息帧结构分析 ===")
    print(f"起始位: {complete_message[0]}")
    print(f"方向位: {complete_message[1:6]} (二进制{complete_message[1:6]} = 十进制{int(complete_message[1:6], 2)})")
    print(f"速度位: {complete_message[6:8]} (二进制{complete_message[6:8]} = 十进制{int(complete_message[6:8], 2)})")
    print(f"校验位: {complete_message[8]}")
    print(f"停止位: {complete_message[9:11]}")
    
    # 验证角度计算
    angle = direction * (360 / 32)
    print()
    print(f"=== 方向验证 ===")
    print(f"方向{direction} 对应角度: {angle}° (每单位 = 360°/32 = 11.25°)")
    
    # 步骤6：差分曼彻斯特编码
    print()
    print("=== 差分曼彻斯特编码 ===")
    dm_encoded = d_m_encode(complete_message)
    print(f"原始消息: {complete_message}")
    print(f"编码结果: {dm_encoded}")
    print(f"编码长度: {len(dm_encoded)} (原始{len(complete_message)}位 → {len(dm_encoded)}个电平)")

    return complete_message, dm_encoded

def d_m_encode(data):
    """差分曼彻斯特编码函数 (复制自encode.py)"""
    encoded = []
    previous_level = 0  # 初始电平设为0
    encoded.append(previous_level)  # 添加起始电平

    print(f"初始电平: {previous_level}")

    for i, bit in enumerate(data):
        print(f"  位{i+1}: '{bit}'")

        # 每个位开始时都有跳变（提供时钟同步）
        previous_level = 1 - previous_level  # 电平翻转
        encoded.append(previous_level)
        print(f"    时钟跳变 → {previous_level}")

        # 根据数据位决定是否在中间跳变
        if bit == '1':
            # 数据位为'1'时，在位中间产生额外跳变
            previous_level = 1 - previous_level
            print(f"    数据'1'跳变 → {previous_level}")
        else:
            print(f"    数据'0'不跳变 → {previous_level}")

        encoded.append(previous_level)

    return encoded

if __name__ == "__main__":
    binary_result, dm_result = test_encoding_example()
    print(f"\n=== 最终结果 ===")
    print(f"二进制消息: {binary_result}")
    print(f"差分曼彻斯特编码: {dm_result}")
